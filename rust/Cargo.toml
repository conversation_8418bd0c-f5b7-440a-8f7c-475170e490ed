[package]
name = "parse-daemon"
version = "0.1.0"
edition = "2024"

[dependencies]
clap = { version = "4.0", features = ["derive"] }
tokio = { version = "1.0", features = ["full"] }
mysql_async = "0.34"
regex = "1.0"
chrono = { version = "0.4", features = ["serde"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
thiserror = "1.0"
rayon = "1.7"
natord = "1.0"
colored = "2.0"
indicatif = "0.17"
env_logger = "0.10"
log = "0.4"
