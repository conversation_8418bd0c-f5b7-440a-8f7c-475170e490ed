use anyhow::{Context, Result};
use colored::*;
use log::error;
use mysql_async::{prelude::*, Pool, PoolConstraints, PoolOpts};
use rayon::prelude::*;
use std::collections::HashMap;
use std::fs::{self, File};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, BufReader, Write};
use std::path::Path;
use std::time::Instant;

use crate::config::*;
use crate::models::*;
use crate::utils::*;
use crate::Args;

pub struct DaemonParser {
    config: Config,
    pool: Pool,
    sensor_list_of_names: Vec<String>,
    sensor_list_of_names_and_addresses: Vec<String>,
    sensor_dict_of_addresses_and_names: HashMap<String, String>,
}

impl DaemonParser {
    pub async fn new(config: Config) -> Result<Self> {
        let pool_opts = PoolOpts::new()
            .with_constraints(PoolConstraints::new(5, 30).unwrap());

        let pool = Pool::new(
            mysql_async::OptsBuilder::default()
                .ip_or_hostname(&config.mysql.host)
                .user(Some(&config.mysql.master_user))
                .pass(Some(&config.mysql.master_password))
                .pool_opts(pool_opts),
        );

        // Test connection
        let conn = pool.get_conn().await
            .context("Failed to connect to MySQL database")?;
        drop(conn);

        let sensor_list_of_names = Sensor::get_list_of_names(&pool).await?;
        let sensor_list_of_names_and_addresses = Sensor::get_list_of_names_and_addresses(&pool).await?;
        let sensor_dict_of_addresses_and_names = Sensor::get_dict_of_addresses_and_names(&pool).await?;

        Ok(Self {
            config,
            pool,
            sensor_list_of_names,
            sensor_list_of_names_and_addresses,
            sensor_dict_of_addresses_and_names,
        })
    }

    pub async fn run(&self, args: Args) -> Result<()> {
        let command = "parse-daemon";
        let today_ymd = get_today_ymd();

        // Check if logs directory exists
        if !Path::new(&self.config.logs_dir).exists() {
            return Err(anyhow::anyhow!("{} does not exist.", self.config.logs_dir));
        }

        // Get list of source logs
        let mut source_logs = get_list_of_files(&self.config.logs_dir, "log")?;
        source_logs = filter_list(
            source_logs,
            args.year_months,
            args.year_month_days,
            args.start_year_month,
            args.start_year_month_day,
            args.end_year_month,
            args.end_year_month_day,
        )?;

        if source_logs.is_empty() {
            return Err(anyhow::anyhow!("no logs."));
        }

        if self.sensor_list_of_names.is_empty() {
            return Err(anyhow::anyhow!("no sensors."));
        }

        for (source_log_index, source_log) in source_logs.iter().enumerate() {
            let source_log_start = Instant::now();

            if !Path::new(source_log).exists() {
                eprintln!("{}", format!("{} does not exist. skipping parsing", to_tilda(source_log)).red());
                continue;
            }

            let log_date = get_date_of_source_log(source_log)?;

            if is_invalid_log_date(&log_date, &today_ymd) {
                continue;
            }

            // Find already accomplished sensors
            let mut already_accomplished = Vec::new();
            if !args.force {
                for sensor_name in &self.sensor_list_of_names {
                    let accomplished_file = format!(
                        "{}/{}/{}/{}-accomplished.log",
                        self.config.daemon.get_logs_parsed_dir(&self.config.logs_parsed_dir),
                        sensor_name,
                        log_date,
                        log_date
                    );
                    if Path::new(&accomplished_file).exists() {
                        already_accomplished.push(sensor_name.clone());
                    }
                }

                if already_accomplished.len() == self.sensor_list_of_names.len() {
                    println!("{}", format!("{}: {} all sensors already parsed, skipping", command, log_date).yellow());
                    continue;
                }
            }

            // Create sensor instances
            let mut sensor_instances: HashMap<String, DaemonParserInstance> = HashMap::new();
            for sensor_name in &self.sensor_list_of_names {
                sensor_instances.insert(
                    sensor_name.clone(),
                    DaemonParserInstance::new(
                        self.config.daemon.slug.clone(),
                        log_date.clone(),
                        sensor_name.clone(),
                    ),
                );
            }

            println!("{}", source_log_info_line(source_log, source_log_index + 1, source_logs.len()));

            // Parse the log file
            self.parse_log_file(source_log, &already_accomplished, &mut sensor_instances).await?;

            // Process each sensor
            for (sensor_name, mut instance) in sensor_instances {
                self.process_sensor_instance(sensor_name, &mut instance, &log_date, command, args.force).await?;
            }

            let source_log_duration = source_log_start.elapsed();
            println!("{}", format!(
                "{} {} {} sensors in {} seconds ({})",
                command,
                log_date,
                self.sensor_list_of_names.len(),
                source_log_duration.as_secs(),
                convert_seconds_to_readable(source_log_duration.as_secs())
            ));
            println!("{}", separator());
        }

        println!("{}", end_of_command_msg(command));
        Ok(())
    }

    async fn parse_log_file(
        &self,
        source_log: &str,
        already_accomplished: &[String],
        sensor_instances: &mut HashMap<String, DaemonParserInstance>,
    ) -> Result<()> {
        let file = File::open(source_log)?;
        let reader = BufReader::new(file);

        println!("parsing...");
        let parse_start = Instant::now();

        let lines: Vec<String> = reader.lines().collect::<Result<Vec<_>, _>>()?;
        
        let parsed_lines: Vec<_> = lines
            .par_iter()
            .map(|line| {
                parse_line(
                    line.trim(),
                    already_accomplished,
                    &self.config.daemon,
                    &self.sensor_list_of_names_and_addresses,
                    &self.sensor_dict_of_addresses_and_names,
                )
            })
            .collect();

        for (sensor_name, parsed_line) in parsed_lines {
            if let (Some(name), Some(line)) = (sensor_name, parsed_line) {
                if let Some(instance) = sensor_instances.get_mut(&name) {
                    instance.rows.push(line);
                }
            }
        }

        let parse_duration = parse_start.elapsed();
        println!("parsed in {} seconds ({})", 
            parse_duration.as_secs(),
            convert_seconds_to_readable(parse_duration.as_secs())
        );

        Ok(())
    }

    async fn process_sensor_instance(
        &self,
        sensor_name: String,
        instance: &mut DaemonParserInstance,
        log_date: &str,
        command: &str,
        force: bool,
    ) -> Result<()> {
        let sensor_start = Instant::now();

        let dest_dir = format!(
            "{}/{}/{}",
            self.config.daemon.get_logs_parsed_dir(&self.config.logs_parsed_dir),
            sensor_name,
            log_date
        );
        let accomplished_file = format!("{}/{}-accomplished.log", dest_dir, log_date);
        let log_file = format!("{}/{}.log", dest_dir, log_date);

        let database_name = create_name_of_database(&self.config.daemon.slug, log_date, &sensor_name);

        // Handle destination directory
        if Path::new(&dest_dir).exists() {
            let should_rm_dest_dir = if force {
                true
            } else if Path::new(&accomplished_file).exists() {
                println!("{}", format!("{}: {} for sensor {} is already parsed. skipping", command, log_date, sensor_name).yellow());
                return Ok(());
            } else {
                true
            };

            if should_rm_dest_dir {
                println!("{}", format!("removing {}", to_tilda(&dest_dir)).red());
                fs::remove_dir_all(&dest_dir)?;
                println!("{}", format!("creating {}", to_tilda(&dest_dir)).green());
                fs::create_dir_all(&dest_dir)?;
            }
        } else {
            println!("{}", format!("creating {}", to_tilda(&dest_dir)).green());
            fs::create_dir_all(&dest_dir)?;
        }

        // Process database operations
        self.process_database_operations(instance, &database_name, &log_file, command).await?;

        // Count errors and warnings
        for row in &instance.rows {
            let event_type = &row.2; // Level column
            if self.config.daemon.event_types_criticals.contains(event_type) {
                instance.daemon_errors_count += 1;
            } else if self.config.daemon.event_types_warnings.contains(event_type) {
                instance.daemon_warnings_count += 1;
            }
        }

        // Create level count table
        self.create_level_count_table(&database_name, instance, &log_file, command).await?;

        // Clean up instance
        instance.truncate_all();

        // Log database info
        save_log(command, &self.config.host_name, &log_file, &format!("database: {}", database_name))?;

        // Create accomplished file
        save_log(command, &self.config.host_name, &accomplished_file, "accomplished")?;

        let sensor_duration = sensor_start.elapsed();
        save_log(
            command,
            &self.config.host_name,
            &log_file,
            &format!("accomplished in {} seconds ({})", 
                sensor_duration.as_secs(),
                convert_seconds_to_readable(sensor_duration.as_secs())
            ),
        )?;
        println!("{}", separator());

        Ok(())
    }

    async fn process_database_operations(
        &self,
        instance: &DaemonParserInstance,
        database_name: &str,
        log_file: &str,
        command: &str,
    ) -> Result<()> {
        let mut conn = self.pool.get_conn().await?;

        // Drop and create database
        save_log(command, &self.config.host_name, log_file, &format!("dropping database {}", database_name))?;
        conn.query_drop(format!("DROP DATABASE IF EXISTS {}", database_name)).await?;

        save_log(command, &self.config.host_name, log_file, &format!("creating database {}", database_name))?;
        conn.query_drop(format!("CREATE DATABASE {}", database_name)).await?;

        let no_of_infiles = get_no_of_infiles(instance.no_of_rows(), self.config.mysql.infile_chunksize);

        if no_of_infiles > 0 {
            save_log(command, &self.config.host_name, log_file, &format!("{} rows will be inserted into database", instance.no_of_rows()))?;

            // Create table
            conn.query_drop(format!("USE {}", database_name)).await?;
            save_log(command, &self.config.host_name, log_file, &format!("creating table {}", self.config.daemon.get_table_name()))?;
            conn.query_drop(format!("CREATE TABLE {} ({})", self.config.daemon.get_table_name(), self.config.daemon.db_columns)).await?;

            save_log(command, &self.config.host_name, log_file, &format!("{} infiles will be created", no_of_infiles))?;

            // Process in batches
            for (batch_index, batch) in evenly_sized_batches(no_of_infiles, 10).enumerate() {
                save_log(command, &self.config.host_name, log_file, &format!("batch {}: writing into {} infiles", batch_index + 1, batch.len()))?;

                let mut infile_paths = Vec::new();

                // Create infiles in parallel
                let batch_vec: Vec<usize> = batch.collect();
                let infile_results: Vec<_> = batch_vec
                    .par_iter()
                    .map(|&infile_index| {
                        let infile_path = create_path_of_infile(database_name, &self.config.daemon.get_table_name(), Some(infile_index));
                        let start_of_chunk = self.config.mysql.infile_chunksize * (infile_index - 1);
                        let end_of_chunk = start_of_chunk + self.config.mysql.infile_chunksize;

                        self.write_into_infile(
                            instance,
                            start_of_chunk,
                            end_of_chunk,
                            &infile_path,
                            infile_index,
                            no_of_infiles,
                            command,
                            log_file,
                        ).map(|_| infile_path)
                    })
                    .collect();

                for result in infile_results {
                    match result {
                        Ok(path) => infile_paths.push(path),
                        Err(e) => error!("Failed to create infile: {}", e),
                    }
                }

                // Insert infiles into database
                let log_msg = format!("batch {}: inserting into {} from {} infiles", batch_index + 1, self.config.daemon.get_table_name(), infile_paths.len());
                save_log(command, &self.config.host_name, log_file, &log_msg)?;

                for infile_path in &infile_paths {
                    if !Path::new(infile_path).exists() {
                        continue;
                    }

                    save_log(command, &self.config.host_name, log_file, &format!("  inserting from {}", infile_path))?;

                    conn.query_drop("SET UNIQUE_CHECKS=0").await?;
                    conn.query_drop("SET FOREIGN_KEY_CHECKS=0").await?;
                    conn.query_drop("START TRANSACTION").await?;

                    let load_query = format!(
                        r#"{} "{}" INTO TABLE {} FIELDS TERMINATED BY "{}" ENCLOSED BY '{}' LINES TERMINATED BY "\n" (ID,{})"#,
                        self.config.mysql.get_infile_statement(),
                        infile_path,
                        self.config.daemon.get_table_name(),
                        self.config.mysql.terminated_by,
                        self.config.mysql.enclosed_by,
                        self.config.daemon.db_keys
                    );

                    conn.query_drop(load_query).await?;
                }

                save_log(command, &self.config.host_name, log_file, "  committing...")?;
                conn.query_drop("COMMIT").await?;

                // Remove infiles
                for infile_path in infile_paths {
                    save_log(command, &self.config.host_name, log_file, &format!("  removing {}", infile_path))?;
                    let _ = fs::remove_file(infile_path);
                }
            }
        }

        Ok(())
    }

    fn write_into_infile(
        &self,
        instance: &DaemonParserInstance,
        start_of_chunk: usize,
        end_of_chunk: usize,
        infile_path: &str,
        infile_index: usize,
        no_of_infiles: usize,
        command: &str,
        log_file: &str,
    ) -> Result<()> {
        let log_msg = format!("  writing into {} ({}/{}): {} -> {}", infile_path, infile_index, no_of_infiles, start_of_chunk, end_of_chunk);
        save_log(command, &self.config.host_name, log_file, &log_msg)?;

        let mut file = File::create(infile_path)?;
        let mut row_id = start_of_chunk;

        let end_index = std::cmp::min(end_of_chunk, instance.rows.len());
        for row in &instance.rows[start_of_chunk..end_index] {
            row_id += 1;
            let line = format!(
                "{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}\n",
                self.config.mysql.enclosed_by, row_id, self.config.mysql.enclosed_by, self.config.mysql.terminated_by,
                self.config.mysql.enclosed_by, row.0, self.config.mysql.enclosed_by, self.config.mysql.terminated_by,
                self.config.mysql.enclosed_by, row.1, self.config.mysql.enclosed_by, self.config.mysql.terminated_by,
                self.config.mysql.enclosed_by, row.2, self.config.mysql.enclosed_by, self.config.mysql.terminated_by,
                self.config.mysql.enclosed_by, row.3, self.config.mysql.enclosed_by, self.config.mysql.terminated_by,
                self.config.mysql.enclosed_by, row.4, self.config.mysql.enclosed_by
            );
            file.write_all(line.as_bytes())?;
        }

        Ok(())
    }

    async fn create_level_count_table(
        &self,
        database_name: &str,
        instance: &DaemonParserInstance,
        log_file: &str,
        command: &str,
    ) -> Result<()> {
        let mut conn = self.pool.get_conn().await?;
        conn.query_drop(format!("USE {}", database_name)).await?;

        let table_name = "levelcounttable";
        let table_columns = format!(
            "ID    {},\n                    Level {},\n                    Count {}",
            self.config.mysql.id_data_type,
            self.config.mysql.default_data_type,
            self.config.mysql.count_data_type
        );

        save_log(command, &self.config.host_name, log_file, &format!("creating table {}", table_name))?;
        conn.query_drop(format!("CREATE TABLE {} ({})", table_name, table_columns)).await?;

        let count_rows = vec![
            ("Error".to_string(), instance.daemon_errors_count),
            ("Warning".to_string(), instance.daemon_warnings_count),
        ];

        save_log(command, &self.config.host_name, log_file, &format!("inserting {} rows into {}", count_rows.len(), table_name))?;

        conn.query_drop("START TRANSACTION").await?;
        for (level, count) in count_rows {
            conn.exec_drop(
                format!("INSERT INTO {} (Level, Count) VALUES (?, ?)", table_name),
                (level, count),
            ).await?;
        }
        conn.query_drop("COMMIT").await?;

        Ok(())
    }
}

pub fn parse_line(
    line: &str,
    already_accomplished: &[String],
    daemon_config: &DaemonConfig,
    sensor_list_of_names_and_addresses: &[String],
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
) -> (Option<String>, Option<(String, String, String, String, String)>) {
    let (sensor_name, parsed_line) = parse_ln(
        line,
        daemon_config,
        sensor_list_of_names_and_addresses,
        sensor_dict_of_addresses_and_names,
    );

    if let Some(ref name) = sensor_name {
        if already_accomplished.contains(name) {
            return (None, None);
        }
    }

    (sensor_name, parsed_line)
}
