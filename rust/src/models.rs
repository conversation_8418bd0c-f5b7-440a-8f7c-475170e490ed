use anyhow::{Context, Result};
use mysql_async::{prelude::*, Pool};
use natord;
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct Sensor {
    pub id: u32,
    pub name: String,
    pub address: Option<String>,
    pub active: bool,
}

impl Sensor {
    pub async fn get_list_of_names(pool: &Pool) -> Result<Vec<String>> {
        let mut conn = pool.get_conn().await?;
        
        let sensors: Vec<Sensor> = conn
            .query_map(
                "SELECT id, name, address, active FROM base_sensor WHERE active = 1",
                |(id, name, address, active)| Sensor {
                    id,
                    name,
                    address,
                    active,
                },
            )
            .await
            .context("Failed to fetch sensors from database")?;

        if sensors.is_empty() {
            return Ok(vec![]);
        }

        let mut names: Vec<String> = sensors.into_iter().map(|s| s.name).collect();
        names.sort_by(|a, b| natord::compare(a, b));
        Ok(names)
    }

    pub async fn get_list_of_names_and_addresses(pool: &Pool) -> Result<Vec<String>> {
        let mut conn = pool.get_conn().await?;
        
        let sensors: Vec<Sensor> = conn
            .query_map(
                "SELECT id, name, address, active FROM base_sensor WHERE active = 1",
                |(id, name, address, active)| Sensor {
                    id,
                    name,
                    address,
                    active,
                },
            )
            .await
            .context("Failed to fetch sensors from database")?;

        if sensors.is_empty() {
            return Ok(vec![]);
        }

        let mut items = Vec::new();
        for sensor in sensors {
            items.push(sensor.name);
            if let Some(addr) = sensor.address {
                items.push(addr);
            }
        }
        
        items.sort_by(|a, b| natord::compare(a, b));
        Ok(items)
    }

    pub async fn get_dict_of_addresses_and_names(pool: &Pool) -> Result<HashMap<String, String>> {
        let mut conn = pool.get_conn().await?;
        
        let sensors: Vec<Sensor> = conn
            .query_map(
                "SELECT id, name, address, active FROM base_sensor WHERE active = 1",
                |(id, name, address, active)| Sensor {
                    id,
                    name,
                    address,
                    active,
                },
            )
            .await
            .context("Failed to fetch sensors from database")?;

        if sensors.is_empty() {
            return Ok(HashMap::new());
        }

        let mut dict = HashMap::new();
        for sensor in sensors {
            if let Some(addr) = sensor.address {
                dict.insert(addr, sensor.name);
            }
        }
        
        Ok(dict)
    }
}

#[derive(Debug, Clone)]
pub struct DaemonParserInstance {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<(String, String, String, String, String)>, // Date, Time, Level, Alert Type, Message
    pub daemon_errors_count: u32,
    pub daemon_warnings_count: u32,
}

impl DaemonParserInstance {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),
            daemon_errors_count: 0,
            daemon_warnings_count: 0,
        }
    }

    pub fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    pub fn truncate_all(&mut self) {
        self.rows.clear();
        self.daemon_errors_count = 0;
        self.daemon_warnings_count = 0;
    }
}
