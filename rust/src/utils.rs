use anyhow::{Context, Result};
use chrono::Local;
use colored::*;
use natord;
use regex::Regex;
use std::collections::HashMap;
use std::fs;
use std::io::Write;
use std::path::Path;

use crate::config::DaemonConfig;

pub fn get_today_ymd() -> String {
    Local::now().format("%Y-%m-%d").to_string()
}

pub fn get_date_of_source_log(log_path: &str) -> Result<String> {
    let path = Path::new(log_path);
    let filename = path.file_name()
        .and_then(|name| name.to_str())
        .context("Invalid log file path")?;
    
    // Extract date from filename like "2023-05-12--Fri.log"
    let re = Regex::new(r"^(\d{4}-\d{2}-\d{2})--.*\.log$")?;
    if let Some(captures) = re.captures(filename) {
        Ok(captures[1].to_string())
    } else {
        Err(anyhow::anyhow!("Could not extract date from log filename: {}", filename))
    }
}

pub fn is_invalid_log_date(log_date: &str, today_ymd: &str) -> bool {
    // Don't process today's logs or future logs
    log_date >= today_ymd
}

pub fn get_list_of_files(directory: &str, extension: &str) -> Result<Vec<String>> {
    let mut files = Vec::new();
    
    if !Path::new(directory).exists() {
        return Ok(files);
    }

    for entry in fs::read_dir(directory)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_file() {
            if let Some(ext) = path.extension() {
                if ext == extension {
                    if let Some(path_str) = path.to_str() {
                        files.push(path_str.to_string());
                    }
                }
            }
        }
    }
    
    files.sort_by(|a, b| natord::compare(a, b));
    Ok(files)
}

pub fn filter_list(
    mut list_of_items: Vec<String>,
    year_months: Option<Vec<String>>,
    year_month_days: Option<Vec<String>>,
    start_year_month: Option<String>,
    start_year_month_day: Option<String>,
    end_year_month: Option<String>,
    end_year_month_day: Option<String>,
) -> Result<Vec<String>> {
    if list_of_items.is_empty() {
        return Ok(list_of_items);
    }

    // Filter by year_months
    if let Some(year_months) = year_months {
        let mut filtered = Vec::new();
        for y_m in year_months {
            for item in &list_of_items {
                let ymd = if is_ymd(item) {
                    item.clone()
                } else {
                    get_date_of_source_log(item)?
                };
                
                if ymd.starts_with(&y_m) {
                    filtered.push(item.clone());
                }
            }
        }
        list_of_items = filtered;
    }
    // Filter by year_month_days
    else if let Some(year_month_days) = year_month_days {
        let mut filtered = Vec::new();
        for y_m_d in year_month_days {
            for item in &list_of_items {
                let ymd = if is_ymd(item) {
                    item.clone()
                } else {
                    get_date_of_source_log(item)?
                };
                
                if ymd == y_m_d {
                    filtered.push(item.clone());
                }
            }
        }
        list_of_items = filtered;
    }
    // Filter by date ranges
    else if let Some(start_ym) = start_year_month {
        let mut filtered = Vec::new();
        for item in &list_of_items {
            let ymd = if is_ymd(item) {
                item.clone()
            } else {
                get_date_of_source_log(item)?
            };
            
            let ym = ymd_to_ym(&ymd);
            let should_add = if let Some(ref end_ym) = end_year_month {
                ym >= start_ym && ym <= *end_ym
            } else {
                ym >= start_ym
            };
            
            if should_add {
                filtered.push(item.clone());
            }
        }
        list_of_items = filtered;
    }
    else if let Some(end_ym) = end_year_month {
        let mut filtered = Vec::new();
        for item in &list_of_items {
            let ymd = if is_ymd(item) {
                item.clone()
            } else {
                get_date_of_source_log(item)?
            };
            
            let ym = ymd_to_ym(&ymd);
            if ym <= end_ym {
                filtered.push(item.clone());
            }
        }
        list_of_items = filtered;
    }
    // Filter by day ranges
    else if let Some(start_ymd) = start_year_month_day {
        let mut filtered = Vec::new();
        for item in &list_of_items {
            let ymd = if is_ymd(item) {
                item.clone()
            } else {
                get_date_of_source_log(item)?
            };
            
            let should_add = if let Some(ref end_ymd) = end_year_month_day {
                ymd >= start_ymd && ymd <= *end_ymd
            } else {
                ymd >= start_ymd
            };
            
            if should_add {
                filtered.push(item.clone());
            }
        }
        list_of_items = filtered;
    }
    else if let Some(end_ymd) = end_year_month_day {
        let mut filtered = Vec::new();
        for item in &list_of_items {
            let ymd = if is_ymd(item) {
                item.clone()
            } else {
                get_date_of_source_log(item)?
            };
            
            if ymd <= end_ymd {
                filtered.push(item.clone());
            }
        }
        list_of_items = filtered;
    }

    Ok(list_of_items)
}

pub fn is_ymd(s: &str) -> bool {
    let re = Regex::new(r"^\d{4}-\d{2}-\d{2}$").unwrap();
    re.is_match(s)
}

pub fn ymd_to_ym(ymd: &str) -> String {
    ymd[..7].to_string() // "2024-03-08" -> "2024-03"
}

pub fn get_no_of_infiles(length: usize, infile_chunksize: usize) -> usize {
    if length == 0 {
        0
    } else {
        (length + infile_chunksize - 1) / infile_chunksize
    }
}

pub fn evenly_sized_batches(total_length: usize, len_of_each_batch: usize) -> impl Iterator<Item = std::ops::Range<usize>> {
    (0..total_length).step_by(len_of_each_batch).map(move |start| {
        let end = std::cmp::min(start + len_of_each_batch, total_length);
        start..end
    })
}

pub fn create_name_of_database(slug: &str, ymd: &str, object_name: &str) -> String {
    let ymd_underscore = ymd.replace('-', "_");
    let object_name_underscore = object_name.replace('-', "_");
    
    if object_name.is_empty() {
        format!("{}__{}", slug, ymd_underscore)
    } else {
        format!("{}__{}_{}", slug, object_name_underscore, ymd_underscore)
    }
}

pub fn create_path_of_infile(database_name: &str, table_name: &str, chunk_number: Option<usize>) -> String {
    let suffix = if let Some(num) = chunk_number {
        format!("__chunk_{}", num)
    } else {
        String::new()
    };
    
    format!("/tmp/infile__{}__{}{}.csv", database_name, table_name, suffix)
}

pub fn source_log_info_line(source_log: &str, source_log_index: usize, source_logs_len: usize) -> String {
    format!("({}/{}) {}", source_log_index, source_logs_len, to_tilda(source_log))
}

pub fn to_tilda(path: &str) -> String {
    if let Some(home) = std::env::var("HOME").ok() {
        path.replace(&home, "~")
    } else {
        path.to_string()
    }
}

pub fn separator() -> String {
    "-".repeat(80)
}

pub fn end_of_command_msg(command: &str) -> String {
    let msg = format!(">>> END of {} <<<", command);
    let centralized = centralize_text(&msg);
    format!("{}", centralized.green())
}

pub fn centralize_text(text: &str) -> String {
    let terminal_width = 80; // Default terminal width
    let text_len = text.len();
    
    if text_len >= terminal_width {
        return text.to_string();
    }
    
    let blanks = terminal_width - text_len;
    let left_margin = blanks / 2;
    format!("{:>width$}{}", "", text, width = left_margin)
}

pub fn convert_seconds_to_readable(seconds: u64) -> String {
    let hours = seconds / 3600;
    let minutes = (seconds % 3600) / 60;
    let secs = seconds % 60;
    
    if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, secs)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, secs)
    } else {
        format!("{}s", secs)
    }
}

pub fn save_log(command: &str, host_name: &str, log_file: &str, message: &str) -> Result<()> {
    let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S");
    let log_entry = format!("{} {} [{}] {}\n", timestamp, host_name, command, message);
    
    if let Some(parent) = Path::new(log_file).parent() {
        fs::create_dir_all(parent)?;
    }
    
    let mut file = fs::OpenOptions::new()
        .create(true)
        .append(true)
        .open(log_file)?;
    
    file.write_all(log_entry.as_bytes())?;
    Ok(())
}

pub fn parse_ln(
    line: &str,
    _daemon_config: &DaemonConfig,
    sensor_list_of_names_and_addresses: &[String],
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
) -> (Option<String>, Option<(String, String, String, String, String)>) {
    if is_invalid_ln(line) {
        return (None, None);
    }

    // Simple daemon log parsing - this is a simplified version
    // In the original Python code, this would call a more complex parser
    // For daemon logs, we'll use a basic regex pattern

    let daemon_pattern = Regex::new(
        r"^(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2}) ([^\s]+) \(([^)]+)\) \[([^\]]+)\] (.*)$"
    ).unwrap();

    if let Some(captures) = daemon_pattern.captures(line) {
        let date = captures[1].to_string();
        let time = captures[2].to_string();
        let sensor_part = captures[3].to_string();
        let level = captures[4].to_string();
        let alert_type = captures[5].to_string();
        let message = captures[6].to_string();

        // Find sensor name from the sensor part
        let sensor_name = find_sensor_name(&sensor_part, sensor_list_of_names_and_addresses, sensor_dict_of_addresses_and_names);

        if let Some(name) = sensor_name {
            return (Some(name), Some((date, time, level, alert_type, message)));
        }
    }

    (None, None)
}

fn is_invalid_ln(line: &str) -> bool {
    if line.is_empty() {
        return true;
    }

    // Check for invalid patterns
    let invalid_patterns = [
        "ERROR name exceeds safe print buffer length",
        "ERROR length byte",
        "leads outside message",
        "Exiting on signal",
        "Now monitoring attacks",
        "spp_arpspoof",
        "because it is a directory, not a file",
    ];

    for pattern in &invalid_patterns {
        if line.contains(pattern) {
            return true;
        }
    }

    // Check if line starts with a digit
    if let Some(first_char) = line.chars().next() {
        if !first_char.is_ascii_digit() {
            return true;
        }
    }

    false
}

fn find_sensor_name(
    sensor_part: &str,
    sensor_list_of_names_and_addresses: &[String],
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
) -> Option<String> {
    // First check if it's an IP address in our dictionary
    if let Some(name) = sensor_dict_of_addresses_and_names.get(sensor_part) {
        return Some(name.clone());
    }

    // Then check if it's directly a sensor name
    if sensor_list_of_names_and_addresses.contains(&sensor_part.to_string()) {
        return Some(sensor_part.to_string());
    }

    None
}
