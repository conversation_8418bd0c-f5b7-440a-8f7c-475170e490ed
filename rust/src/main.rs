use anyhow::Result;
use clap::Parser;
use log::info;

mod config;
mod models;
mod parser;
mod utils;

use config::Config;
use parser::DaemonParser;

#[derive(Parser)]
#[command(name = "parse-daemon")]
#[command(about = "Parse Daemon logs and insert into MySQL database")]
struct Args {
    /// Year-months to process (e.g., 2024-01,2024-02)
    #[arg(long, value_delimiter = ',')]
    year_months: Option<Vec<String>>,

    /// Year-month-days to process (e.g., 2024-01-15,2024-01-16)
    #[arg(long, value_delimiter = ',')]
    year_month_days: Option<Vec<String>>,

    /// Start year-month (e.g., 2024-01)
    #[arg(long)]
    start_year_month: Option<String>,

    /// Start year-month-day (e.g., 2024-01-15)
    #[arg(long)]
    start_year_month_day: Option<String>,

    /// End year-month (e.g., 2024-12)
    #[arg(long)]
    end_year_month: Option<String>,

    /// End year-month-day (e.g., 2024-12-31)
    #[arg(long)]
    end_year_month_day: Option<String>,

    /// Force reprocessing of already processed logs
    #[arg(long)]
    force: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();

    let args = Args::parse();
    let config = Config::load()?;

    info!("Starting parse-daemon");

    let daemon_parser = DaemonParser::new(config).await?;
    daemon_parser.run(args).await?;

    info!("parse-daemon completed successfully");
    Ok(())
}
