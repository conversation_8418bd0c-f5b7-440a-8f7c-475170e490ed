use anyhow::{Context, Result};
use std::collections::HashMap;
use std::env;

#[derive(Debug, <PERSON><PERSON>)]
pub struct Config {
    pub logs_dir: String,
    pub logs_parsed_dir: String,
    pub host_name: String,
    pub mysql: MySQLConfig,
    pub daemon: DaemonConfig,
}

#[derive(Debug, <PERSON>lone)]
pub struct MySQLConfig {
    pub host: String,
    pub master_user: String,
    pub master_password: String,
    pub r_user: String,
    pub r_user_password: String,
    pub infile_chunksize: usize,
    pub pool_chunksize: usize,
    pub enclosed_by: String,
    pub terminated_by: String,
    pub db_name_separator: String,
    pub table_name_separator: String,
    pub id_data_type: String,
    pub date_data_type: String,
    pub time_data_type: String,
    pub default_data_type: String,
    pub count_data_type: String,
    pub non_dated_databases: Vec<String>,
}

#[derive(Debug, <PERSON>lone)]
pub struct DaemonConfig {
    pub title: String,
    pub slug: String,
    pub filterby: String,
    pub filterby_is_in_alert_type: bool,
    pub event_types: Vec<String>,
    pub event_types_criticals: Vec<String>,
    pub event_types_warnings: Vec<String>,
    pub db_headers: Vec<String>,
    pub db_columns: String,
    pub db_keys: String,
    pub db_marks: String,
}

impl Config {
    pub fn load() -> Result<Self> {
        let logs_dir = env::var("LOGS_DIR")
            .context("LOGS_DIR environment variable not set")?;
        
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
            .context("LOGS_PARSED_DIR environment variable not set")?;
        
        let host_name = env::var("HOST_NAME")
            .unwrap_or_else(|_| "localhost".to_string());

        let mysql = MySQLConfig {
            host: env::var("MYSQL_HOST")
                .context("MYSQL_HOST environment variable not set")?,
            master_user: env::var("MYSQL_MASTER")
                .context("MYSQL_MASTER environment variable not set")?,
            master_password: env::var("MYSQL_MASTER_PASSWD")
                .context("MYSQL_MASTER_PASSWD environment variable not set")?,
            r_user: env::var("MYSQL_R_USER")
                .context("MYSQL_R_USER environment variable not set")?,
            r_user_password: env::var("MYSQL_R_USER_PASSWD")
                .context("MYSQL_R_USER_PASSWD environment variable not set")?,
            infile_chunksize: 5_000_000,
            pool_chunksize: 100_000,
            enclosed_by: "".to_string(), // '"' caused errors
            terminated_by: "-*@*-".to_string(),
            db_name_separator: "__".to_string(),
            table_name_separator: "__".to_string(),
            id_data_type: "INT AUTO_INCREMENT PRIMARY KEY".to_string(),
            date_data_type: "DATE".to_string(),
            time_data_type: "TIME".to_string(),
            default_data_type: "MEDIUMTEXT".to_string(),
            count_data_type: "INT".to_string(),
            non_dated_databases: vec![
                "geolocation".to_string(),
                "malicious".to_string(),
            ],
        };

        let daemon = DaemonConfig {
            title: "Daemon".to_string(),
            slug: "daemon".to_string(),
            filterby: "".to_string(), // No specific filter for daemon logs
            filterby_is_in_alert_type: false,
            event_types: vec![
                "daemon/alert".to_string(),
                "daemon/crit".to_string(),
                "daemon/debug".to_string(),
                "daemon/emerg".to_string(),
                "daemon/err".to_string(),
                "daemon/info".to_string(),
                "daemon/notice".to_string(),
                "daemon/warning".to_string(),
            ],
            event_types_criticals: vec![
                "daemon/alert".to_string(),
                "daemon/crit".to_string(),
                "daemon/emerg".to_string(),
                "daemon/err".to_string(),
            ],
            event_types_warnings: vec![
                "daemon/warning".to_string(),
            ],
            db_headers: vec![
                "Date".to_string(),
                "Time".to_string(),
                "Level".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
            ],
            db_columns: format!(
                "ID           {},\n        Date         {},\n        Time         {},\n        Level        {},\n        `Alert Type` {},\n        Message      {}",
                mysql.id_data_type,
                mysql.date_data_type,
                mysql.time_data_type,
                mysql.default_data_type,
                mysql.default_data_type,
                mysql.default_data_type
            ),
            db_keys: "Date,\n        Time,\n        Level,\n        `Alert Type`,\n        Message".to_string(),
            db_marks: "%s,%s,%s,%s,%s".to_string(),
        };

        Ok(Config {
            logs_dir,
            logs_parsed_dir,
            host_name,
            mysql,
            daemon,
        })
    }
}

impl DaemonConfig {
    pub fn get_table_name(&self) -> String {
        format!("{}table", self.slug)
    }

    pub fn get_logs_parsed_dir(&self, logs_parsed_dir: &str) -> String {
        format!("{}/{}", logs_parsed_dir, self.slug)
    }

    pub fn get_select_statement(&self) -> String {
        format!("SELECT * FROM {}", self.get_table_name())
    }
}

impl MySQLConfig {
    pub fn get_infile_statement(&self) -> String {
        "LOAD DATA INFILE".to_string()
    }

    pub fn get_master_creds(&self) -> HashMap<String, String> {
        let mut creds = HashMap::new();
        creds.insert("host".to_string(), self.host.clone());
        creds.insert("user".to_string(), self.master_user.clone());
        creds.insert("password".to_string(), self.master_password.clone());
        creds
    }
}
